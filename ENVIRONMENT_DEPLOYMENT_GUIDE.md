# Environment-Specific Deployment Guide

This guide explains how to use the enhanced `submit_deploy_v2.py` script with environment-specific parameters for deploying to multiple Azure ML environments.

## Overview

The deployment script now supports multiple environments through command-line parameters, allowing you to deploy to different environments (dev, test, staging, prod) with environment-specific configurations.

## Features

✅ **Environment Parameter Support**: Deploy to specific environments via command-line arguments  
✅ **Environment-Specific Configuration**: Load different Azure ML workspace configurations per environment  
✅ **Comprehensive Logging**: Loguru logging with timestamped output to logs/ directory  
✅ **User Confirmation Prompts**: Mandatory confirmation before irreversible deployment actions  
✅ **Dry Run Mode**: Validate configuration without actually deploying  
✅ **Error Handling**: Comprehensive validation for missing environment variables  
✅ **Backward Compatibility**: Existing usage patterns continue to work  

## Usage

### Basic Syntax

```bash
python deploy/submit_deploy_v2.py <environment> [options]
```

### Supported Environments

- `test` - Test environment (uses current active configuration in .env)
- `dev` - Development environment
- `sit` - System Integration Test environment
- `uat` - User Acceptance Test environment
- `preprod` - Pre-production environment
- `prod` - Production environment

### Command-Line Options

- `--dry-run` - Perform validation without actually deploying
- `--skip-confirmation` - Skip user confirmation prompts (use with caution!)
- `--verbose` - Enable verbose logging output
- `--help` - Show help message and usage examples

## Examples

### Deploy to Test Environment
```bash
python deploy/submit_deploy_v2.py test
```

### Deploy to Production with Dry Run
```bash
python deploy/submit_deploy_v2.py prod --dry-run
```

### Deploy to Development (Skip Confirmation)
```bash
python deploy/submit_deploy_v2.py dev --skip-confirmation
```

### Validate Configuration Only
```bash
python deploy/submit_deploy_v2.py prod --dry-run --skip-confirmation
```

## Environment Configuration

### .env File Structure

The script uses environment-specific variables in your `.env` file. Each environment has its own set of variables with specific suffixes:

```bash
# Test environment (no suffix - backward compatibility)
AZURE_RESOURCE_GROUP=test-resource-group
AZURE_ML_WORKSPACE_NAME=test-workspace
MODEL_NAME=test-model

# Development environment (_DEV suffix)
AZURE_RESOURCE_GROUP_DEV=dev-resource-group
AZURE_ML_WORKSPACE_NAME_DEV=dev-workspace
MODEL_NAME_DEV=dev-model

# Production environment (_PROD suffix)
AZURE_SUBSCRIPTION_ID_PROD=prod-subscription-id
AZURE_RESOURCE_GROUP_PROD=prod-resource-group
AZURE_ML_WORKSPACE_NAME_PROD=prod-workspace
MODEL_NAME_PROD=prod-model
```

### Required Variables per Environment

Each environment requires these variables (with appropriate suffix):

- `AZURE_SUBSCRIPTION_ID[_ENV]` - Azure subscription ID
- `AZURE_RESOURCE_GROUP[_ENV]` - Resource group name
- `AZURE_ML_WORKSPACE_NAME[_ENV]` - Azure ML workspace name
- `MODEL_NAME[_ENV]` - Model name
- `ENV_NAME[_ENV]` - Environment name
- `ENDPOINT_NAME[_ENV]` - Endpoint name
- `DEPLOYMENT_NAME[_ENV]` - Deployment name

### Shared Configuration

These variables are shared across all environments:

- `AZURE_TENANT_ID` - Azure tenant ID
- `AZURE_CLIENT_ID` - Azure client ID
- `AZURE_CLIENT_SECRET` - Azure client secret
- `MODEL_FILE_PATH` - Path to model file
- `CONDA_FILE_PATH` - Path to conda environment file
- `DEPLOYMENT_PYTHONPATH` - Python path for deployment
- `MODEL_FILE_MD5_HASH` - MD5 hash for model validation (optional)

## Security Features

### Environment Variable Validation
- ✅ Validates all required environment variables are present
- ✅ Provides clear error messages for missing variables
- ✅ Logs which values are loaded from environment variables

### User Confirmation
- ✅ Shows target environment clearly in confirmation prompt
- ✅ Displays critical deployment information before proceeding
- ✅ Lists potential risks and costs
- ✅ Can be skipped with `--skip-confirmation` flag

### Logging
- ✅ Comprehensive Loguru logging with emoji conventions
- ✅ Timestamped log files in logs/ directory
- ✅ Logs environment variable loading with masking for sensitive data
- ✅ Tracks which environment is being targeted

## Error Handling

### Invalid Environment Names
```bash
$ python deploy/submit_deploy_v2.py invalid
error: argument environment: invalid choice: 'invalid' (choose from 'test', 'dev', 'sit', 'uat', 'preprod', 'prod')
```

### Missing Environment Variables
```bash
$ python deploy/submit_deploy_v2.py dev
❌ Missing required environment variables for dev: AZURE_RESOURCE_GROUP_DEV, AZURE_ML_WORKSPACE_NAME_DEV, ...
```

### File Validation
- ✅ Validates model file exists and shows size
- ✅ Validates conda environment file exists
- ✅ Validates scoring script exists
- ✅ Performs MD5 hash validation if configured

## Migration from Previous Version

The script maintains backward compatibility. Existing usage patterns continue to work:

### Old Usage (still works)
```bash
python deploy/submit_deploy_v2.py
```

### New Usage (recommended)
```bash
python deploy/submit_deploy_v2.py test
```

## Testing

A test script is provided to verify functionality:

```bash
python test_deployment_environments.py
```

This tests:
- Help functionality
- Invalid environment rejection
- Test environment dry run
- Missing configuration error handling

## Best Practices

1. **Always use dry run first**: `--dry-run` to validate configuration
2. **Use environment-specific variables**: Don't mix environment configurations
3. **Review confirmation prompts**: Carefully check target environment before confirming
4. **Monitor logs**: Check timestamped log files for detailed deployment information
5. **Use appropriate environments**: Follow your organization's deployment pipeline

## Troubleshooting

### Common Issues

1. **Missing environment variables**: Uncomment the appropriate section in `.env`
2. **Wrong environment**: Check the target environment in confirmation prompt
3. **File not found**: Verify model and conda file paths are correct
4. **Authentication errors**: Ensure Azure credentials are properly configured

### Getting Help

```bash
python deploy/submit_deploy_v2.py --help
```

This shows all available options and usage examples.
