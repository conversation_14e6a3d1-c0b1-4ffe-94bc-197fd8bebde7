#!/usr/bin/env python3
"""
Azure ML Deployment Script - Refactored Version

This refactored version addresses critical security vulnerabilities, improves error handling,
implements proper code structure, and follows Python best practices.

Key Improvements Made:
1. SECURITY FIXES:
   - Removed hardcoded credentials with secure environment variable validation
   - Implemented absolute path handling with proper validation
   - Added comprehensive input validation for all parameters

2. ERROR HANDLING:
   - Added try-catch blocks around all Azure ML operations
   - Implemented timeout mechanisms with exponential backoff
   - Added model/environment existence validation
   - Comprehensive logging with meaningful error messages

3. CODE STRUCTURE:
   - Separated concerns into focused, single-responsibility functions
   - Added proper main function with entry point
   - Implemented comprehensive docstrings and type hints
   - Removed dead code and unused variables

4. PERFORMANCE OPTIMIZATIONS:
   - Replaced inefficient list iteration with direct retrieval methods
   - Implemented exponential backoff instead of fixed sleep intervals
   - Added proper resource management and connection handling

5. PYTHON BEST PRACTICES:
   - Organized imports properly (standard library, third-party, local)
   - Added comprehensive type hints for all functions
   - Followed PEP 8 naming conventions and formatting
   - Implemented proper logging instead of print statements

Author: Refactored for enterprise-grade security and maintainability
Date: 2025-05-29
"""

import os
import sys
import time
from contextlib import contextmanager
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, Optional, Tuple

# Third-party imports
import pendulum
from azure.ai.ml import MLClient
from azure.ai.ml.constants import AssetTypes
from azure.ai.ml.entities import CodeConfiguration, Environment, ManagedOnlineDeployment, ManagedOnlineEndpoint, Model
from azure.core.exceptions import ResourceNotFoundError
from azure.identity import DefaultAzureCredential
from dotenv import load_dotenv
from loguru import logger


# Configure loguru logging
def setup_logging() -> str:
    """
    Configure loguru logging with timestamped log files.

    Returns:
        Path to the log file created for this deployment run
    """
    # Remove default handler
    logger.remove()

    # Create logs directory if it doesn't exist
    logs_dir = Path('logs')
    logs_dir.mkdir(exist_ok=True)

    # Generate timestamp for log filename
    timestamp = pendulum.now().format('YYYY-MM-DD_HH-mm-ss')
    log_filename = f'deployment_{timestamp}.log'
    log_filepath = logs_dir / log_filename

    # Add console handler with colored output
    logger.add(
        sys.stdout,
        format='<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>',
        level='INFO',
        colorize=True,
    )

    # Add file handler with detailed format
    logger.add(
        str(log_filepath),
        format='{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} - {message}',
        level='DEBUG',
        rotation='10 MB',
        retention='30 days',
        compression='zip',
    )

    logger.info(f'Logging configured - Log file: {log_filepath}')
    return str(log_filepath)


# Initialize logging
log_file_path = setup_logging()


@dataclass
class DeploymentConfig:
    """Configuration class for deployment parameters with validation."""

    # Azure credentials
    subscription_id: str
    resource_group: str
    workspace_name: str

    # Deployment parameters
    model_name: str
    env_name: str
    endpoint_name: str
    deployment_name: str

    # File paths (configurable via environment variables)
    model_file_path: str
    conda_file_path: str
    deployment_pythonpath: str

    # MD5 hash validation (optional)
    model_file_md5_hash: Optional[str]

    # Optional parameters with defaults
    # instance_type: str = 'STANDARD_NC4AS_T4_V3'
    instance_type: str = 'STANDARD_DS3_V2'
    instance_count: int = 1
    timeout_seconds: int = 1800  # 30 minutes
    max_retries: int = 1

    def __post_init__(self) -> None:
        """Validate configuration after initialization."""
        self._validate_required_fields()
        self._validate_azure_naming_conventions()

    def _validate_required_fields(self) -> None:
        """Validate that all required fields are present and non-empty."""
        required_fields = [
            'subscription_id',
            'resource_group',
            'workspace_name',
            'model_name',
            'env_name',
            'endpoint_name',
            'deployment_name',
            'model_file_path',
            'conda_file_path',
            'deployment_pythonpath',
        ]

        for field in required_fields:
            value = getattr(self, field)
            if not value or not isinstance(value, str) or not value.strip():
                raise ValueError(f"Required field '{field}' is missing or empty")

    def _validate_azure_naming_conventions(self) -> None:
        """Validate Azure naming conventions for resources."""
        # Azure resource names must be 3-24 characters, alphanumeric and hyphens only
        naming_fields = ['endpoint_name', 'deployment_name']

        for field in naming_fields:
            value = getattr(self, field)
            if not (3 <= len(value) <= 24):
                raise ValueError(f'{field} must be between 3-24 characters')

            if not value.replace('-', '').replace('_', '').isalnum():
                raise ValueError(f'{field} must contain only alphanumeric characters and hyphens')


class AzureMLDeploymentError(Exception):
    """Custom exception for Azure ML deployment errors."""

    pass


def display_deployment_summary(config: DeploymentConfig) -> None:
    """
    Display a comprehensive summary of the deployment configuration.

    Args:
        config: Deployment configuration to display
    """
    logger.info('=' * 80)
    logger.info('DEPLOYMENT CONFIGURATION SUMMARY')
    logger.info('=' * 80)

    # Azure Environment Details
    logger.info('🔧 Azure Environment:')
    logger.info(f'   Subscription ID: {config.subscription_id}')
    logger.info(f'   Resource Group:  {config.resource_group}')
    logger.info(f'   Workspace:       {config.workspace_name}')

    # Deployment Details
    logger.info('🚀 Deployment Configuration:')
    logger.info(f'   Model Name:      {config.model_name}')
    logger.info(f'   Environment:     {config.env_name}')
    logger.info(f'   Endpoint Name:   {config.endpoint_name}')
    logger.info(f'   Deployment Name: {config.deployment_name}')

    # Infrastructure Details
    logger.info('💻 Infrastructure:')
    logger.info(f'   Instance Type:   {config.instance_type}')
    logger.info(f'   Instance Count:  {config.instance_count}')
    logger.info(f'   Timeout:         {config.timeout_seconds} seconds')
    logger.info(f'   Max Retries:     {config.max_retries}')

    # File Validation
    logger.info('📁 File Validation:')

    # Check model file
    model_path = Path(config.model_file_path).resolve()
    model_status = '✅ EXISTS' if model_path.exists() else '❌ MISSING'
    model_size = f'({model_path.stat().st_size / (1024 * 1024):.1f} MB)' if model_path.exists() else ''
    logger.info(f'   Model File:      {model_status} {model_size}')
    logger.info(f'   Model Path:      {model_path}')

    # Check conda file
    conda_path = Path(config.conda_file_path).resolve()
    conda_status = '✅ EXISTS' if conda_path.exists() else '❌ MISSING'
    logger.info(f'   Conda File:      {conda_status}')
    logger.info(f'   Conda Path:      {conda_path}')

    # Check scoring script
    score_path = Path('Python/score.py').resolve()
    score_status = '✅ EXISTS' if score_path.exists() else '❌ MISSING'
    logger.info(f'   Scoring Script:  {score_status}')
    logger.info(f'   Script Path:     {score_path}')

    # Environment Variables
    logger.info('🔐 Environment Variables:')
    env_vars = [
        'AZURE_SUBSCRIPTION_ID',
        'AZURE_RESOURCE_GROUP',
        'AZURE_ML_WORKSPACE_NAME',
        'MODEL_NAME',
        'ENV_NAME',
        'ENDPOINT_NAME',
        'DEPLOYMENT_NAME',
        'MODEL_FILE_PATH',
        'CONDA_FILE_PATH',
        'DEPLOYMENT_PYTHONPATH',
        'MODEL_FILE_MD5_HASH',
    ]
    for var in env_vars:
        value = os.getenv(var, 'NOT SET')
        masked_value = value[:8] + '...' if len(value) > 8 and 'ID' in var else value
        status = '✅' if value != 'NOT SET' else '❌'
        logger.info(f'   {var}: {status} {masked_value}')

    logger.info('=' * 80)


def get_user_confirmation(config: DeploymentConfig) -> bool:
    """
    Get user confirmation before proceeding with deployment.

    Args:
        config: Deployment configuration

    Returns:
        True if user confirms, False otherwise
    """
    logger.info('⚠️  DEPLOYMENT CONFIRMATION REQUIRED')
    logger.info('=' * 50)

    # Show critical deployment information
    logger.warning(f'🎯 Target Environment: {config.workspace_name}')
    logger.warning(f'🚀 Deployment Name: {config.deployment_name}')
    logger.warning(f'📦 Model: {config.model_name}')
    logger.warning(f'💰 Instance Type: {config.instance_type} (Count: {config.instance_count})')

    # Show potential risks
    logger.info('⚠️  Potential Risks:')
    logger.info('   • This will create/update Azure ML resources')
    logger.info('   • Existing deployments with the same name may be overwritten')
    logger.info('   • Azure costs will be incurred for compute resources')
    logger.info('   • The deployment process may take 15-30 minutes')

    logger.info('=' * 50)

    # Get user input with retry logic
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            response = input('Do you want to proceed with this deployment? (y/n): ').strip().lower()

            if response in ['y', 'yes']:
                logger.info('✅ User confirmed deployment. Proceeding...')
                return True
            elif response in ['n', 'no']:
                logger.warning('❌ User cancelled deployment.')
                return False
            else:
                logger.warning(f"Invalid response: '{response}'. Please enter 'y' or 'n'.")
                if attempt < max_attempts - 1:
                    logger.info(f'Attempt {attempt + 2}/{max_attempts}')

        except (EOFError, KeyboardInterrupt):
            logger.warning('❌ User interrupted deployment confirmation.')
            return False

    logger.error('❌ Too many invalid attempts. Cancelling deployment.')
    return False


class AzureMLDeployer:
    """
    Enterprise-grade Azure ML deployment manager with comprehensive error handling,
    security best practices, and performance optimizations.
    """

    def __init__(self, config: DeploymentConfig) -> None:
        """
        Initialize the deployer with validated configuration.

        Args:
            config: Validated deployment configuration
        """
        self.config = config
        self._client: Optional[MLClient] = None
        logger.info('Initialized AzureMLDeployer with configuration')

    @property
    def client(self) -> MLClient:
        """Lazy-loaded ML client with connection validation."""
        if self._client is None:
            self._client = self._create_ml_client()
        return self._client

    def _create_ml_client(self) -> MLClient:
        """
        Create and validate Azure ML client connection.

        Returns:
            Authenticated MLClient instance

        Raises:
            AzureMLDeploymentError: If client creation or authentication fails
        """
        try:
            logger.info('Creating Azure ML client connection')

            # Use DefaultAzureCredential for secure authentication
            credential = DefaultAzureCredential()

            client = MLClient(
                credential=credential,
                subscription_id=self.config.subscription_id,
                resource_group_name=self.config.resource_group,
                workspace_name=self.config.workspace_name,
            )

            # Validate connection by attempting to get workspace info
            workspace = client.workspaces.get(self.config.workspace_name)
            workspace_name = getattr(workspace, 'name', self.config.workspace_name)
            logger.info(f'Successfully connected to workspace: {workspace_name}')

            return client

        except Exception as e:
            error_msg = f'Failed to create Azure ML client: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def _calculate_file_md5(self, file_path: Path) -> str:
        """
        Calculate MD5 hash of a file in chunks for memory efficiency.

        Args:
            file_path: Path to the file to calculate MD5 hash for

        Returns:
            MD5 hash as lowercase hexadecimal string

        Raises:
            AzureMLDeploymentError: If file I/O operations fail
        """
        import hashlib

        try:
            md5_hash = hashlib.md5()

            with open(file_path, 'rb') as f:
                # Read file in chunks to handle large files efficiently
                for chunk in iter(lambda: f.read(8192), b''):
                    md5_hash.update(chunk)

            return md5_hash.hexdigest().lower()

        except Exception as e:
            error_msg = f'Failed to calculate MD5 hash for file {file_path}: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def _exponential_backoff_wait(self, attempt: int, base_delay: float = 1.0) -> None:
        """
        Implement exponential backoff for retry operations.

        Args:
            attempt: Current attempt number (0-based)
            base_delay: Base delay in seconds
        """
        delay = base_delay * (2**attempt)
        max_delay = 60.0  # Cap at 60 seconds
        actual_delay = min(delay, max_delay)

        logger.info(f'Waiting {actual_delay:.1f} seconds before retry (attempt {attempt + 1})')
        time.sleep(actual_delay)

    def _wait_for_operation_completion(
        self, poller: Any, operation_name: str, timeout_seconds: Optional[int] = None
    ) -> Any:
        """
        Wait for Azure ML operation completion with timeout and proper error handling.

        Args:
            poller: Azure ML operation poller
            operation_name: Human-readable operation name for logging
            timeout_seconds: Optional timeout override

        Returns:
            Operation result

        Raises:
            AzureMLDeploymentError: If operation fails or times out
        """
        timeout = timeout_seconds or self.config.timeout_seconds
        start_time = time.time()

        logger.info(f'Starting {operation_name} operation (timeout: {timeout}s)')

        try:
            while not poller.done():
                elapsed = time.time() - start_time

                if elapsed > timeout:
                    error_msg = f'{operation_name} operation timed out after {timeout} seconds'
                    logger.error(error_msg)
                    raise AzureMLDeploymentError(error_msg)

                # Log progress every 30 seconds
                if int(elapsed) % 30 == 0 and elapsed > 0:
                    logger.info(f'{operation_name} in progress... ({elapsed:.0f}s elapsed)')

                time.sleep(5)  # Check every 5 seconds

            result = poller.result()
            elapsed = time.time() - start_time
            logger.info(f'{operation_name} completed successfully in {elapsed:.1f} seconds')

            return result

        except Exception as e:
            error_msg = f'{operation_name} operation failed: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def register_model(self) -> Model:
        """
        Register or retrieve existing model with proper validation and error handling.

        Returns:
            Registered Model instance

        Raises:
            AzureMLDeploymentError: If model registration fails
        """
        try:
            logger.info('🔍 MODEL REGISTRATION PHASE')
            logger.info('=' * 40)
            logger.info(f'Checking for existing model: {self.config.model_name}')

            # Try to get existing model first (more efficient than listing all)
            try:
                model = self.client.models.get(name=self.config.model_name, label='latest')
                logger.info(f'✅ Found existing model: {model.name} (version: {model.version})')
                logger.info(f'   Model ID: {getattr(model, "id", "N/A")}')

                # Handle SystemData object properly (Azure ML SDK v2)
                creation_context = getattr(model, 'creation_context', None)
                if creation_context:
                    if hasattr(creation_context, 'created_at'):
                        created_at_utc = getattr(creation_context, 'created_at', None)
                        if created_at_utc:
                            # Convert to Australia/Sydney timezone
                            try:
                                # Parse the datetime and convert to Sydney timezone
                                dt = pendulum.parse(str(created_at_utc))
                                # Check if it's a DateTime instance specifically
                                if isinstance(dt, pendulum.DateTime):
                                    created_at_sydney = dt.in_timezone('Australia/Sydney')
                                    created_at = created_at_sydney.format('YYYY-MM-DD HH:mm:ss zz')
                                else:
                                    # If not a DateTime, try to create one from the string
                                    dt_str = str(created_at_utc)
                                    if 'T' in dt_str or ' ' in dt_str:  # Looks like a datetime string
                                        dt_parsed = pendulum.parse(dt_str)
                                        if isinstance(dt_parsed, pendulum.DateTime):
                                            created_at_sydney = dt_parsed.in_timezone('Australia/Sydney')
                                            created_at = created_at_sydney.format('YYYY-MM-DD HH:mm:ss zz')
                                        else:
                                            created_at = str(created_at_utc)
                                    else:
                                        created_at = str(created_at_utc)
                            except Exception:
                                # Fallback to original value if parsing fails
                                created_at = str(created_at_utc)
                        else:
                            created_at = 'N/A'
                    else:
                        created_at = 'N/A'
                    logger.info(f'   Created: {created_at}')
                else:
                    logger.info('   Created: N/A')

                logger.info(f'   Description: {getattr(model, "description", "N/A")}')

                # MD5 validation for existing Azure ML model
                if self.config.model_file_md5_hash:
                    logger.info('🔍 Performing MD5 validation for existing Azure ML model')
                    model_tags = getattr(model, 'tags', {}) or {}
                    azure_md5_hash = model_tags.get('md5_hash')

                    if azure_md5_hash:
                        logger.info(f'📋 Azure model MD5 hash: {azure_md5_hash}')
                        logger.info(f'📋 Expected MD5 hash: {self.config.model_file_md5_hash}')

                        if azure_md5_hash.lower() == self.config.model_file_md5_hash.lower():
                            logger.info('✅ MD5 validation passed for existing Azure ML model')
                        else:
                            error_msg = (
                                f'Model file MD5 validation failed for existing Azure ML model. '
                                f'Expected: {self.config.model_file_md5_hash}, '
                                f'Azure model hash: {azure_md5_hash}. '
                                f'Please verify model file integrity and ensure MODEL_FILE_MD5_HASH environment variable is correct.'
                            )
                            logger.error(f'❌ {error_msg}')
                            raise AzureMLDeploymentError(error_msg)
                    else:
                        logger.warning('⚠️ No MD5 hash tag found on existing Azure ML model - cannot validate integrity')
                        logger.warning('   Consider re-registering the model to include MD5 hash validation')
                else:
                    logger.warning(
                        '⚠️ MD5 validation skipped for existing Azure ML model - MODEL_FILE_MD5_HASH not configured'
                    )

                return model

            except ResourceNotFoundError:
                logger.info(f'ℹ️  Model {self.config.model_name} not found, creating new registration')

            # Validate model file path
            model_path = Path(self.config.model_file_path).resolve()
            logger.info(f'📁 Validating model file: {model_path}')

            if not model_path.exists():
                raise AzureMLDeploymentError(f'Model file not found: {model_path}')

            # Log model file details
            model_size = model_path.stat().st_size
            logger.info('✅ Model file validation successful:')
            logger.info(f'   File size: {model_size / (1024 * 1024):.2f} MB')
            logger.info(f'   Full path: {model_path}')

            # MD5 validation for new model registration
            calculated_md5_hash = self._calculate_file_md5(model_path)
            logger.info(f'📋 Calculated MD5 hash: {calculated_md5_hash}')

            if self.config.model_file_md5_hash:
                logger.info(f'📋 Expected MD5 hash: {self.config.model_file_md5_hash}')

                if calculated_md5_hash.lower() == self.config.model_file_md5_hash.lower():
                    logger.info('✅ MD5 validation passed for local model file')
                else:
                    error_msg = (
                        f'Model file MD5 validation failed. '
                        f'Expected: {self.config.model_file_md5_hash}, '
                        f'Actual: {calculated_md5_hash}. '
                        f'File path: {model_path}. '
                        f'Please verify model file integrity and ensure MODEL_FILE_MD5_HASH environment variable is correct.'
                    )
                    logger.error(f'❌ {error_msg}')
                    raise AzureMLDeploymentError(error_msg)
            else:
                logger.warning('⚠️ MD5 validation skipped - MODEL_FILE_MD5_HASH not configured')

            # Create new model registration with MD5 hash tag
            model_tags = {
                'md5_hash': calculated_md5_hash,
                'registered_date': pendulum.now().isoformat(),
                'file_size_mb': f'{model_size / (1024 * 1024):.2f}',
            }

            file_model = Model(
                name=self.config.model_name,
                path=str(model_path),
                type=AssetTypes.CUSTOM_MODEL,
                description=f'Model registered on {pendulum.now().isoformat()}',
                tags=model_tags,
            )

            logger.info(f'🚀 Registering new model from path: {model_path}')
            logger.info(f'   Model name: {self.config.model_name}')
            logger.info(f'   Model type: {AssetTypes.CUSTOM_MODEL}')

            model = self.client.models.create_or_update(file_model)

            logger.info('✅ Successfully registered model:')
            logger.info(f'   Name: {model.name}')
            logger.info(f'   Version: {model.version}')
            logger.info(f'   ID: {getattr(model, "id", "N/A")}')
            logger.info('=' * 40)

            return model

        except Exception as e:
            error_msg = f'Failed to register model {self.config.model_name}: {str(e)}'
            logger.error(f'❌ {error_msg}')
            raise AzureMLDeploymentError(error_msg) from e

    def create_environment(self) -> Environment:
        """
        Create or retrieve existing environment with validation.

        Returns:
            Environment instance

        Raises:
            AzureMLDeploymentError: If environment creation fails
        """
        try:
            logger.info('🌍 ENVIRONMENT SETUP PHASE')
            logger.info('=' * 40)
            logger.info(f'Checking for existing environment: {self.config.env_name}')

            # Try to get existing environment first
            try:
                env = self.client.environments.get(name=self.config.env_name, label='latest')
                logger.info(f'✅ Found existing environment: {env.name} (version: {env.version})')
                logger.info(f'   Environment ID: {getattr(env, "id", "N/A")}')
                logger.info(f'   Base Image: {getattr(env, "image", "N/A")}')
                logger.info(f'   Description: {getattr(env, "description", "N/A")}')
                return env

            except ResourceNotFoundError:
                logger.info(f'ℹ️  Environment {self.config.env_name} not found, creating new one')

            # Validate conda file path
            conda_file_path = Path(self.config.conda_file_path).resolve()
            logger.info(f'📁 Validating conda file: {conda_file_path}')

            if not conda_file_path.exists():
                raise AzureMLDeploymentError(f'Conda file not found: {conda_file_path}')

            # Log conda file details
            conda_size = conda_file_path.stat().st_size
            logger.info('✅ Conda file validation successful:')
            logger.info(f'   File size: {conda_size} bytes')
            logger.info(f'   Full path: {conda_file_path}')

            # Read and log some conda file content for verification
            try:
                with open(conda_file_path, 'r') as f:
                    lines = f.readlines()[:10]  # First 10 lines
                logger.info('   Conda file preview (first 10 lines):')
                for i, line in enumerate(lines, 1):
                    logger.info(f'     {i}: {line.strip()}')
                if len(lines) == 10:
                    logger.info('     ... (file continues)')
            except Exception as e:
                logger.warning(f'   Could not read conda file content: {e}')

            # Create new environment
            base_image = 'mcr.microsoft.com/azureml/openmpi3.1.2-ubuntu18.04'
            env_conda = Environment(
                image=base_image,
                conda_file=str(conda_file_path),
                name=self.config.env_name,
                description=f'Environment created on {pendulum.now().isoformat()}',
            )

            logger.info('🚀 Creating new environment:')
            logger.info(f'   Environment name: {self.config.env_name}')
            logger.info(f'   Base image: {base_image}')
            logger.info(f'   Conda file: {conda_file_path}')

            env = self.client.environments.create_or_update(env_conda)

            logger.info('✅ Successfully created environment:')
            logger.info(f'   Name: {env.name}')
            logger.info(f'   Version: {env.version}')
            logger.info(f'   ID: {getattr(env, "id", "N/A")}')
            logger.info('=' * 40)

            return env

        except Exception as e:
            error_msg = f'Failed to create environment {self.config.env_name}: {str(e)}'
            logger.error(f'❌ {error_msg}')
            raise AzureMLDeploymentError(error_msg) from e

    def create_endpoint(self) -> ManagedOnlineEndpoint:
        """
        Create or retrieve existing online endpoint.

        Returns:
            ManagedOnlineEndpoint instance

        Raises:
            AzureMLDeploymentError: If endpoint creation fails
        """
        try:
            logger.info(f'Checking for existing endpoint: {self.config.endpoint_name}')

            # Try to get existing endpoint first
            try:
                endpoint = self.client.online_endpoints.get(name=self.config.endpoint_name)
                logger.info(f'Found existing endpoint: {endpoint.name}')
                # Convert to ManagedOnlineEndpoint if needed
                if isinstance(endpoint, ManagedOnlineEndpoint):
                    return endpoint
                else:
                    # Create a new ManagedOnlineEndpoint with the same properties
                    managed_endpoint = ManagedOnlineEndpoint(
                        name=endpoint.name,
                        auth_mode=getattr(endpoint, 'auth_mode', 'key'),
                        description=getattr(endpoint, 'description', ''),
                        tags=getattr(endpoint, 'tags', {}),
                    )
                    return managed_endpoint

            except ResourceNotFoundError:
                logger.info(f'Endpoint {self.config.endpoint_name} not found, creating new one')

            # Create new endpoint
            endpoint = ManagedOnlineEndpoint(
                name=self.config.endpoint_name,
                auth_mode='key',
                description=f'Endpoint created on {pendulum.now().isoformat()}',
                tags={'CreatedBy': 'AzureMLDeployer', 'Version': 'v2_refactored'},
            )

            logger.info(f'Creating new endpoint: {self.config.endpoint_name}')
            poller = self.client.online_endpoints.begin_create_or_update(endpoint)

            # Wait for endpoint creation with timeout
            self._wait_for_operation_completion(poller, 'endpoint creation')

            # Retrieve the created endpoint
            created_endpoint = self.client.online_endpoints.get(name=self.config.endpoint_name)
            logger.info(f'Successfully created endpoint: {created_endpoint.name}')

            # Ensure we return a ManagedOnlineEndpoint
            if isinstance(created_endpoint, ManagedOnlineEndpoint):
                return created_endpoint
            else:
                # Convert to ManagedOnlineEndpoint
                managed_endpoint = ManagedOnlineEndpoint(
                    name=created_endpoint.name,
                    auth_mode=getattr(created_endpoint, 'auth_mode', 'key'),
                    description=getattr(created_endpoint, 'description', ''),
                    tags=getattr(created_endpoint, 'tags', {}),
                )
                return managed_endpoint

        except Exception as e:
            error_msg = f'Failed to create endpoint {self.config.endpoint_name}: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def create_deployment(
        self, model: Model, environment: Environment, endpoint: ManagedOnlineEndpoint
    ) -> ManagedOnlineDeployment:
        """
        Create managed online deployment with comprehensive configuration.

        Args:
            model: Registered model instance
            environment: Environment instance
            endpoint: Online endpoint instance

        Returns:
            ManagedOnlineDeployment instance

        Raises:
            AzureMLDeploymentError: If deployment creation fails
        """
        try:
            logger.info('🚀 DEPLOYMENT CREATION PHASE')
            logger.info('=' * 40)

            # Validate scoring script path
            scoring_script_path = Path('Python/score.py').resolve()
            code_path = scoring_script_path.parent

            logger.info('📁 Validating deployment files:')
            logger.info(f'   Scoring script: {scoring_script_path}')
            logger.info(f'   Code directory: {code_path}')

            if not scoring_script_path.exists():
                raise AzureMLDeploymentError(f'Scoring script not found: {scoring_script_path}')

            # Log scoring script details
            script_size = scoring_script_path.stat().st_size
            logger.info('✅ Scoring script validation successful:')
            logger.info(f'   File size: {script_size / 1024:.2f} KB')

            # Check for additional required files in code directory
            required_files = ['score.py']
            optional_files = ['resources/', 'logs/']

            for file_name in required_files:
                file_path = code_path / file_name
                status = '✅ EXISTS' if file_path.exists() else '❌ MISSING'
                logger.info(f'   {file_name}: {status}')

            for file_name in optional_files:
                file_path = code_path / file_name
                status = '✅ EXISTS' if file_path.exists() else 'ℹ️  OPTIONAL'
                logger.info(f'   {file_name}: {status}')

            logger.info('🔧 Deployment configuration:')
            logger.info(f'   Deployment name: {self.config.deployment_name}')
            logger.info(f'   Endpoint name: {self.config.endpoint_name}')
            logger.info(f'   Model: {model.name} (v{model.version})')
            logger.info(f'   Environment: {environment.name} (v{environment.version})')
            logger.info(f'   Instance type: {self.config.instance_type}')
            logger.info(f'   Instance count: {self.config.instance_count}')

            # Create deployment configuration
            env_vars = {'PYTHONPATH': self.config.deployment_pythonpath}
            deployment_tags = {
                'DeploymentDate': pendulum.now().isoformat(),
                'ModelName': model.name,
                'ModelVersion': model.version,
                'EnvironmentName': environment.name,
                'EnvironmentVersion': environment.version,
                'CreatedBy': 'AzureMLDeployer',
            }

            logger.info('🏷️  Environment variables:')
            for key, value in env_vars.items():
                logger.info(f'   {key}: {value}')

            logger.info('🏷️  Deployment tags:')
            for key, value in deployment_tags.items():
                logger.info(f'   {key}: {value}')

            deployment = ManagedOnlineDeployment(
                name=self.config.deployment_name,
                endpoint_name=self.config.endpoint_name,
                model=model,
                code_configuration=CodeConfiguration(code=str(code_path), scoring_script='score.py'),
                environment=environment,
                instance_type=self.config.instance_type,
                instance_count=self.config.instance_count,
                environment_variables=env_vars,
                tags=deployment_tags,
            )

            # Start deployment
            logger.info('🚀 Starting deployment creation...')
            logger.warning('⏱️  This operation may take 15-30 minutes to complete')
            deployment_poller = self.client.online_deployments.begin_create_or_update(deployment)

            # Wait for deployment completion with extended timeout
            self._wait_for_operation_completion(
                deployment_poller, 'deployment creation', timeout_seconds=self.config.timeout_seconds
            )

            # Retrieve the created deployment
            created_deployment = self.client.online_deployments.get(
                name=self.config.deployment_name, endpoint_name=self.config.endpoint_name
            )

            logger.info('✅ Successfully created deployment:')
            logger.info(f'   Name: {created_deployment.name}')
            logger.info(f'   Endpoint: {self.config.endpoint_name}')
            logger.info(f'   Status: {getattr(created_deployment, "provisioning_state", "N/A")}')
            logger.info('=' * 40)

            # Ensure we return a ManagedOnlineDeployment
            if isinstance(created_deployment, ManagedOnlineDeployment):
                return created_deployment
            else:
                # This should not happen in practice, but handle it for type safety
                logger.warning('Retrieved deployment is not a ManagedOnlineDeployment, converting...')
                return deployment  # Return the original deployment object we created

        except Exception as e:
            error_msg = f'Failed to create deployment {self.config.deployment_name}: {str(e)}'
            logger.error(f'❌ {error_msg}')
            raise AzureMLDeploymentError(error_msg) from e

    def configure_traffic(self, endpoint: ManagedOnlineEndpoint) -> Dict[str, int]:
        """
        Configure traffic routing to the deployment.

        Args:
            endpoint: Online endpoint instance

        Returns:
            Traffic configuration dictionary

        Raises:
            AzureMLDeploymentError: If traffic configuration fails
        """
        try:
            logger.info(f'Configuring traffic for deployment: {self.config.deployment_name}')

            # Update endpoint traffic configuration
            endpoint.traffic = {self.config.deployment_name: 100}

            # Apply traffic configuration
            traffic_poller = self.client.online_endpoints.begin_create_or_update(endpoint)

            # Wait for traffic configuration completion
            self._wait_for_operation_completion(traffic_poller, 'traffic configuration')

            # Verify traffic configuration
            updated_endpoint = self.client.online_endpoints.get(name=self.config.endpoint_name)
            traffic_config = updated_endpoint.traffic

            logger.info(f'Traffic configuration completed: {traffic_config}')
            return traffic_config

        except Exception as e:
            error_msg = f'Failed to configure traffic for {self.config.deployment_name}: {str(e)}'
            logger.error(error_msg)
            raise AzureMLDeploymentError(error_msg) from e

    def deploy_with_retry(self) -> Tuple[ManagedOnlineEndpoint, ManagedOnlineDeployment, Dict[str, int]]:
        """
        Execute complete deployment process with retry logic.

        Returns:
            Tuple of (endpoint, deployment, traffic_config)

        Raises:
            AzureMLDeploymentError: If deployment fails after all retries
        """
        last_exception = None

        for attempt in range(self.config.max_retries):
            try:
                logger.info('🔄 DEPLOYMENT EXECUTION PHASE')
                logger.info('=' * 50)
                logger.info(f'Starting deployment attempt {attempt + 1}/{self.config.max_retries}')

                if attempt > 0:
                    logger.warning('⚠️  This is a retry attempt due to previous failure')

                # Step 1: Register model
                logger.info('📦 STEP 1/5: Model Registration')
                model = self.register_model()

                # Step 2: Create environment
                logger.info('🌍 STEP 2/5: Environment Setup')
                environment = self.create_environment()

                # Step 3: Create endpoint
                logger.info('🌐 STEP 3/5: Endpoint Creation')
                endpoint = self.create_endpoint()

                # Step 4: Create deployment
                logger.info('🚀 STEP 4/5: Deployment Creation')
                deployment = self.create_deployment(model, environment, endpoint)

                # Step 5: Configure traffic
                logger.info('🚦 STEP 5/5: Traffic Configuration')
                traffic_config = self.configure_traffic(endpoint)

                logger.info('✅ All deployment steps completed successfully!')
                logger.info('=' * 50)
                return endpoint, deployment, traffic_config

            except Exception as e:
                last_exception = e
                logger.error(f'❌ Deployment attempt {attempt + 1} failed: {str(e)}')

                if attempt < self.config.max_retries - 1:
                    logger.warning('🔄 Preparing for retry...')
                    self._exponential_backoff_wait(attempt)
                    logger.info(f'🔄 Retrying deployment (attempt {attempt + 2}/{self.config.max_retries})')
                else:
                    logger.error('💥 All deployment attempts failed')

        # If we get here, all retries failed
        error_msg = f'Deployment failed after {self.config.max_retries} attempts'
        if last_exception:
            error_msg += f'. Last error: {str(last_exception)}'

        logger.error(f'💥 {error_msg}')
        raise AzureMLDeploymentError(error_msg)


def load_and_validate_environment() -> DeploymentConfig:
    """
    Load and validate environment variables for deployment configuration.

    Returns:
        Validated DeploymentConfig instance

    Raises:
        AzureMLDeploymentError: If environment validation fails
    """
    try:
        # Load environment variables from .env file
        load_dotenv()
        logger.info('📋 Loading environment variables from .env file')

        # Required environment variables
        required_env_vars = [
            'AZURE_SUBSCRIPTION_ID',
            'AZURE_RESOURCE_GROUP',
            'AZURE_ML_WORKSPACE_NAME',
            'MODEL_NAME',
            'ENV_NAME',
            'ENDPOINT_NAME',
            'DEPLOYMENT_NAME',
        ]

        # Optional environment variables with defaults (for backward compatibility)
        optional_env_vars = {
            'MODEL_FILE_PATH': 'Python/resources/autolodge_20250605.h5',
            'CONDA_FILE_PATH': 'configs/autolodge.yaml',
            'DEPLOYMENT_PYTHONPATH': '/var/azureml-app/autolodge_retrained_deploy',
        }

        # Check for missing required environment variables
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            raise AzureMLDeploymentError(f'Missing required environment variables: {", ".join(missing_vars)}')

        # Load optional environment variables with defaults and logging
        model_file_path = os.getenv('MODEL_FILE_PATH', optional_env_vars['MODEL_FILE_PATH'])
        conda_file_path = os.getenv('CONDA_FILE_PATH', optional_env_vars['CONDA_FILE_PATH'])
        deployment_pythonpath = os.getenv('DEPLOYMENT_PYTHONPATH', optional_env_vars['DEPLOYMENT_PYTHONPATH'])
        model_file_md5_hash = os.getenv('MODEL_FILE_MD5_HASH')  # Optional, no default

        # Log which values are being used
        logger.info('🔧 Configuration values loaded:')
        logger.info(f'   Model Name: {os.getenv("MODEL_NAME")}')

        if os.getenv('MODEL_FILE_PATH'):
            logger.info(f'   Model File Path: {model_file_path} (from environment variable)')
        else:
            logger.info(f'   Model File Path: {model_file_path} (using default for backward compatibility)')

        if os.getenv('CONDA_FILE_PATH'):
            logger.info(f'   Conda File Path: {conda_file_path} (from environment variable)')
        else:
            logger.info(f'   Conda File Path: {conda_file_path} (using default for backward compatibility)')

        if os.getenv('DEPLOYMENT_PYTHONPATH'):
            logger.info(f'   Deployment PYTHONPATH: {deployment_pythonpath} (from environment variable)')
        else:
            logger.info(f'   Deployment PYTHONPATH: {deployment_pythonpath} (using default for backward compatibility)')

        if model_file_md5_hash:
            logger.info(f'   Expected MD5 Hash: {model_file_md5_hash} (from environment variable)')
        else:
            logger.info('   Expected MD5 Hash: Not configured (MD5 validation will be skipped)')

        # Create configuration with validation
        config = DeploymentConfig(
            subscription_id=os.getenv('AZURE_SUBSCRIPTION_ID') or '',
            resource_group=os.getenv('AZURE_RESOURCE_GROUP') or '',
            workspace_name=os.getenv('AZURE_ML_WORKSPACE_NAME') or '',
            model_name=os.getenv('MODEL_NAME') or '',
            env_name=os.getenv('ENV_NAME') or '',
            endpoint_name=os.getenv('ENDPOINT_NAME') or '',
            deployment_name=os.getenv('DEPLOYMENT_NAME') or '',
            model_file_path=model_file_path,
            conda_file_path=conda_file_path,
            deployment_pythonpath=deployment_pythonpath,
            model_file_md5_hash=model_file_md5_hash,
        )

        logger.info('✅ Environment configuration loaded and validated successfully')
        return config

    except Exception as e:
        error_msg = f'Failed to load environment configuration: {str(e)}'
        logger.error(error_msg)
        raise AzureMLDeploymentError(error_msg) from e


@contextmanager
def deployment_context():
    """Context manager for deployment operations with proper cleanup."""
    logger.info('Starting deployment context')
    try:
        yield
        logger.info('Deployment context completed successfully')
    except Exception as e:
        logger.error(f'Deployment context failed: {str(e)}')
        raise
    finally:
        logger.info('Deployment context cleanup completed')


def main() -> None:
    """
    Main entry point for the deployment script.

    Raises:
        SystemExit: With appropriate exit code based on success/failure
    """
    try:
        with deployment_context():
            logger.info('🚀 AZURE ML DEPLOYMENT SCRIPT STARTED')
            logger.info('=' * 60)

            # Load and validate configuration
            logger.info('📋 STEP 1: Loading and validating configuration...')
            config = load_and_validate_environment()
            logger.info('✅ Configuration loaded successfully')

            # Display comprehensive deployment summary
            logger.info('📊 STEP 2: Displaying deployment summary...')
            display_deployment_summary(config)

            # Get user confirmation before proceeding
            logger.info('🔐 STEP 3: User confirmation required...')
            if not get_user_confirmation(config):
                logger.warning('❌ Deployment cancelled by user')
                sys.exit(0)

            # Create deployer instance
            logger.info('🔧 STEP 4: Initializing Azure ML deployer...')
            deployer = AzureMLDeployer(config)
            logger.info('✅ Deployer initialized successfully')

            # Execute deployment with retry logic
            logger.info('🚀 STEP 5: Executing deployment...')
            logger.info('⚠️  WARNING: Irreversible deployment operations will now begin!')
            endpoint, deployment, traffic_config = deployer.deploy_with_retry()

            # Log final results
            logger.info('🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!')
            logger.info('=' * 60)
            logger.info('FINAL DEPLOYMENT SUMMARY')
            logger.info('=' * 60)
            logger.info(f'✅ Endpoint: {endpoint.name}')
            logger.info(f'✅ Deployment: {deployment.name}')
            logger.info(f'✅ Traffic Configuration: {traffic_config}')
            logger.info(f'✅ Instance Type: {config.instance_type}')
            logger.info(f'✅ Instance Count: {config.instance_count}')
            logger.info(f'✅ Workspace: {config.workspace_name}')
            logger.info(f'✅ Resource Group: {config.resource_group}')
            logger.info('=' * 60)

            # Log next steps
            logger.info('📋 NEXT STEPS:')
            logger.info('   1. Test the deployed endpoint with sample data')
            logger.info('   2. Monitor deployment performance and logs')
            logger.info('   3. Update traffic allocation if needed')
            logger.info('   4. Set up monitoring and alerting')
            logger.info('=' * 60)

            logger.success('🎉 Deployment completed successfully!')

    except AzureMLDeploymentError as e:
        logger.error(f'❌ Deployment failed: {str(e)}')
        logger.error('💡 Check the log file for detailed error information')
        sys.exit(1)
    except KeyboardInterrupt:
        logger.warning('⚠️  Deployment interrupted by user')
        logger.info('💡 You can safely restart the deployment script')
        sys.exit(130)
    except Exception as e:
        logger.error(f'💥 Unexpected error during deployment: {str(e)}')
        logger.error('💡 This may be a system or network issue - check connectivity and try again')
        sys.exit(1)


if __name__ == '__main__':
    main()
